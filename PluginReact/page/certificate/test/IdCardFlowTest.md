# 身份证拍摄流程测试文档

## 功能概述
实现了身份证拍摄的新流程，用户点击身份证图片后会跳转到前置页面选择要拍摄的身份证面，然后进入相应的拍摄页面。

## 新增页面

### 1. IdCardSelectionPage (身份证选择前置页面)
- **路径**: `PluginReact/page/certificate/IdCardSelectionPage.tsx`
- **功能**: 显示身份证国徽面和人像面的选择界面
- **路由名称**: `IdCardSelectionPage`

### 2. IdCardCapturePageNational (身份证国徽面拍摄页面)
- **路径**: `PluginReact/page/certificate/IdCardCapturePageNational.tsx`
- **功能**: 拍摄身份证国徽面
- **路由名称**: `IdCardCapturePageNational`

### 3. IdCardCapturePagePortrait (身份证人像面拍摄页面)
- **路径**: `PluginReact/page/certificate/IdCardCapturePagePortrait.tsx`
- **功能**: 拍摄身份证人像面
- **路由名称**: `IdCardCapturePagePortrait`

## 修改的页面

### 1. CertificationAuthenticationPage.tsx
- **修改内容**: 
  - 去除了手动输入身份证号的切换按钮
  - 修改身份证图片点击逻辑，点击时跳转到前置页面
  - 添加了处理从拍摄页面返回数据的逻辑

### 2. RouterCertificate.ts
- **修改内容**: 添加了三个新页面的路由配置

## 流程说明

### 原流程
1. 用户进入身份认证页面
2. 可以选择"手动输入身份证号"或直接拍摄
3. 点击身份证图片直接进入OCR拍摄

### 新流程
1. 用户进入身份认证页面
2. 点击身份证图片（人像面或国徽面）
3. 跳转到身份证选择前置页面 (IdCardSelectionPage)
4. 用户选择要拍摄的身份证面
5. 跳转到相应的拍摄页面：
   - 选择国徽面 → IdCardCapturePageNational
   - 选择人像面 → IdCardCapturePagePortrait
6. 拍摄完成后：
   - 国徽面拍摄完成 → 跳转到人像面拍摄
   - 人像面拍摄完成 → 返回身份认证页面，显示拍摄结果

## 测试用例

### 测试用例1: 基本流程测试
1. 进入身份认证页面
2. 点击身份证人像面图片
3. 验证是否跳转到身份证选择页面
4. 点击"身份证国徽面"
5. 验证是否跳转到国徽面拍摄页面
6. 完成拍摄
7. 验证是否跳转到人像面拍摄页面
8. 完成拍摄
9. 验证是否返回身份认证页面并显示拍摄的图片

### 测试用例2: 直接拍摄人像面
1. 进入身份认证页面
2. 点击身份证人像面图片
3. 在选择页面点击"身份证人像面"
4. 完成拍摄
5. 验证是否返回身份认证页面

### 测试用例3: 返回按钮测试
1. 在各个页面测试返回按钮功能
2. 验证页面跳转是否正确

### 测试用例4: 重新拍摄功能
1. 在拍摄页面完成拍摄
2. 点击"重新拍摄"按钮
3. 验证是否可以重新拍摄

## 注意事项

1. **图标资源**: 使用了项目中已有的图标 `certification_cys_take_picture`
2. **路由配置**: 已在 `RouterCertificate.ts` 中添加了新页面的路由配置
3. **数据传递**: 通过路由参数传递图片URL和其他必要数据
4. **OCR识别**: 在返回身份认证页面后会自动触发OCR识别
5. **埋点统计**: 添加了相应的埋点统计代码

## 可能的问题

1. **图标显示**: 如果图标不显示，可能需要检查图标资源是否存在
2. **路由跳转**: 确保所有页面都已正确注册到路由配置中
3. **数据传递**: 检查页面间的数据传递是否正确
4. **OCR功能**: 确保OCR识别功能正常工作

## 后续优化建议

1. 可以添加拍摄指导动画
2. 可以添加图片质量检测
3. 可以添加拍摄失败重试机制
4. 可以优化页面切换动画效果
