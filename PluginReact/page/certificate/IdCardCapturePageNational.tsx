import React from 'react';
import {StyleSheet, Text, View, Dimensions} from 'react-native';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import UITitleView from '../widget/UITitleView';
import UITouchableOpacity from '../widget/UITouchableOpacity';
import UIImage from '../widget/UIImage';
import UIButton from '../widget/UIButton';
import {RouterUtils} from '../util/RouterUtils';
import {RouterUrl} from '../base/RouterUrl';
import {onEvent} from '../base/native/UITrackingAction';
import {Method} from '../util/NativeModulesTools';
import TextUtils from '../util/TextUtils';

const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

interface State extends BaseState {
    init: boolean;
    capturedImageUrl?: string;
}

/**
 * 注释: 身份证国徽面拍摄页面
 * 时间: 2025/01/31
 * <AUTHOR> Assistant
 */
export default class IdCardCapturePageNational extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    toSave?: string;
    callback?: Function;

    constructor(props) {
        super(props);
        this.state = {
            init: false,
        };
        this.toSave = this.getParams().toSave ?? '';
        this.callback = this.getCallBack();
    }

    componentDidMount() {
        super.componentDidMount();
        this.setState({init: true});
        onEvent({pageId: 'sfzghm', tableId: 'sfzghm', event: 'view'});
    }

    /**
     * 开始拍照
     */
    startCapture = () => {
        onEvent({pageId: 'sfzghm', tableId: 'sfzghm_kspz'});
        
        // 调用原生拍照功能
        Method.openCameraAndLibNew(true, 1, (code, json) => {
            try {
                if (code === 200) {
                    const result = JSON.parse(json);
                    if (result && result.length > 0) {
                        this.uploadImage(result[0]);
                    }
                } else if (code === -2 || code === -3) {
                    const result = JSON.parse(json);
                    Method.showToast(result.msg || '拍照失败');
                }
            } catch (e) {
                Method.showToast('拍照失败，请重试');
            }
        });
    };

    /**
     * 上传图片
     */
    uploadImage = async (filePath: string) => {
        this._showWaitDialog();
        
        try {
            const json = await Method.upFileNew(filePath);
            const result = JSON.parse(json);
            
            if (TextUtils.equals('200', result.code)) {
                this.setState({capturedImageUrl: result.url});
                this._dismissWait();
                this._showToast('拍摄成功');
            } else {
                this._dismissWait();
                Method.showToast(result.msg || '上传失败');
            }
        } catch (e) {
            this._dismissWait();
            Method.showToast('上传失败，请重试');
        }
    };

    /**
     * 重新拍摄
     */
    retakePhoto = () => {
        onEvent({pageId: 'sfzghm', tableId: 'sfzghm_cxpz'});
        this.setState({capturedImageUrl: undefined});
        this.startCapture();
    };

    /**
     * 确认使用
     */
    confirmUse = () => {
        onEvent({pageId: 'sfzghm', tableId: 'sfzghm_qrsy'});
        
        // 跳转到人像面拍摄页面
        RouterUtils.skipRouter(RouterUrl.ReactPage, {
            page: 'IdCardCapturePagePortrait',
            data: {
                toSave: this.toSave,
                cardType: 'portrait',
                nationalImageUrl: this.state.capturedImageUrl, // 传递国徽面图片URL
            },
            callBack: this.callback,
            action: 'replace',
        });
    };

    /**
     * 相册上传
     */
    uploadFromAlbum = () => {
        onEvent({pageId: 'sfzghm', tableId: 'sfzghm_xcsc'});
        
        Method.openCameraAndLibNew(false, 1, (code, json) => {
            try {
                if (code === 200) {
                    const result = JSON.parse(json);
                    if (result && result.length > 0) {
                        this.uploadImage(result[0]);
                    }
                } else if (code === -2 || code === -3) {
                    const result = JSON.parse(json);
                    Method.showToast(result.msg || '选择图片失败');
                }
            } catch (e) {
                Method.showToast('选择图片失败，请重试');
            }
        });
    };

    render() {
        return (
            <View style={styles.container}>
                <UITitleView 
                    title={'身份证国徽面'} 
                    clickBack={() => {
                        this._goBack();
                        onEvent({pageId: 'sfzghm', tableId: 'sfzghm_fh'});
                    }}
                />
                
                {this.state.init && (
                    <View style={styles.content}>
                        {/* 拍摄说明 */}
                        <View style={styles.instructionSection}>
                            <Text style={styles.instructionTitle}>请按以下要求进行拍摄</Text>
                            <View style={styles.tipsList}>
                                <View style={styles.tipItem}>
                                    <View style={styles.tipDot} />
                                    <Text style={styles.tipText}>证件完整置于拍摄框内</Text>
                                </View>
                                <View style={styles.tipItem}>
                                    <View style={styles.tipDot} />
                                    <Text style={styles.tipText}>四角齐全，无遮挡</Text>
                                </View>
                                <View style={styles.tipItem}>
                                    <View style={styles.tipDot} />
                                    <Text style={styles.tipText}>将证件从保护套中拿出，避免反光</Text>
                                </View>
                            </View>
                        </View>

                        {/* 拍摄区域 */}
                        <View style={styles.captureArea}>
                            {this.state.capturedImageUrl ? (
                                // 显示拍摄结果
                                <View style={styles.resultContainer}>
                                    <UIImage
                                        source={this.state.capturedImageUrl}
                                        style={styles.capturedImage}
                                        resizeMode="contain"
                                    />
                                    <View style={styles.resultActions}>
                                        <UITouchableOpacity 
                                            style={styles.retakeButton}
                                            onPress={this.retakePhoto}
                                        >
                                            <Text style={styles.retakeButtonText}>重新拍摄</Text>
                                        </UITouchableOpacity>
                                        <UIButton 
                                            text="确认使用"
                                            style={styles.confirmButton}
                                            onPress={this.confirmUse}
                                        />
                                    </View>
                                </View>
                            ) : (
                                // 显示拍摄界面
                                <View style={styles.captureContainer}>
                                    <View style={styles.captureFrame}>
                                        <UIImage 
                                            source={'certification_driver_license_template_diagram_5'}
                                            style={styles.templateImage}
                                            resizeMode="contain"
                                        />
                                        <View style={styles.frameOverlay}>
                                            <View style={styles.frameCorner} />
                                            <View style={[styles.frameCorner, styles.frameCornerTopRight]} />
                                            <View style={[styles.frameCorner, styles.frameCornerBottomLeft]} />
                                            <View style={[styles.frameCorner, styles.frameCornerBottomRight]} />
                                        </View>
                                    </View>
                                    
                                    <View style={styles.captureActions}>
                                        <UITouchableOpacity
                                            style={styles.albumButton}
                                            onPress={this.uploadFromAlbum}
                                        >
                                            <UIImage
                                                source={'certification_cys_take_picture'}
                                                style={styles.albumIcon}
                                            />
                                        </UITouchableOpacity>
                                        
                                        <UITouchableOpacity 
                                            style={styles.captureButton}
                                            onPress={this.startCapture}
                                        >
                                            <View style={styles.captureButtonInner} />
                                        </UITouchableOpacity>
                                        
                                        <View style={styles.placeholder} />
                                    </View>
                                </View>
                            )}
                        </View>
                    </View>
                )}
                
                {this._initCommView()}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#000',
    },
    content: {
        flex: 1,
    },
    instructionSection: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        margin: 16,
        borderRadius: 8,
        padding: 16,
    },
    instructionTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 12,
    },
    tipsList: {
        marginLeft: 8,
    },
    tipItem: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 6,
    },
    tipDot: {
        width: 4,
        height: 4,
        borderRadius: 2,
        backgroundColor: '#5086fc',
        marginRight: 8,
    },
    tipText: {
        fontSize: 13,
        color: '#666',
        flex: 1,
    },
    captureArea: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    captureContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
    },
    captureFrame: {
        width: screenWidth * 0.8,
        height: screenWidth * 0.8 * 0.63, // 身份证比例
        position: 'relative',
        marginBottom: 60,
    },
    templateImage: {
        width: '100%',
        height: '100%',
        opacity: 0.3,
    },
    frameOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    frameCorner: {
        position: 'absolute',
        width: 20,
        height: 20,
        borderColor: '#5086fc',
        borderWidth: 3,
        top: -2,
        left: -2,
        borderRightWidth: 0,
        borderBottomWidth: 0,
    },
    frameCornerTopRight: {
        right: -2,
        left: 'auto',
        borderLeftWidth: 0,
        borderRightWidth: 3,
    },
    frameCornerBottomLeft: {
        bottom: -2,
        top: 'auto',
        borderTopWidth: 0,
        borderBottomWidth: 3,
    },
    frameCornerBottomRight: {
        bottom: -2,
        right: -2,
        top: 'auto',
        left: 'auto',
        borderTopWidth: 0,
        borderLeftWidth: 0,
        borderBottomWidth: 3,
        borderRightWidth: 3,
    },
    captureActions: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: screenWidth * 0.8,
        paddingHorizontal: 20,
    },
    albumButton: {
        width: 50,
        height: 50,
        borderRadius: 25,
        backgroundColor: 'rgba(255, 255, 255, 0.3)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    albumIcon: {
        width: 24,
        height: 24,
        tintColor: '#fff',
    },
    captureButton: {
        width: 70,
        height: 70,
        borderRadius: 35,
        backgroundColor: '#fff',
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 4,
        borderColor: 'rgba(255, 255, 255, 0.5)',
    },
    captureButtonInner: {
        width: 50,
        height: 50,
        borderRadius: 25,
        backgroundColor: '#5086fc',
    },
    placeholder: {
        width: 50,
        height: 50,
    },
    resultContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
        paddingHorizontal: 16,
    },
    capturedImage: {
        width: screenWidth * 0.8,
        height: screenWidth * 0.8 * 0.63,
        borderRadius: 8,
        marginBottom: 40,
    },
    resultActions: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
        paddingHorizontal: 20,
    },
    retakeButton: {
        flex: 1,
        height: 50,
        borderRadius: 25,
        borderWidth: 1,
        borderColor: '#fff',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 10,
    },
    retakeButtonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: 'bold',
    },
    confirmButton: {
        flex: 1,
        marginLeft: 10,
    },
});
