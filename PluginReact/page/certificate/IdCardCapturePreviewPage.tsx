import React from 'react';
import {StyleSheet, Text, View, Dimensions} from 'react-native';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import UITouchableOpacity from '../widget/UITouchableOpacity';
import UIImage from '../widget/UIImage';
import UIButton from '../widget/UIButton';
import {RouterUtils} from '../util/RouterUtils';
import {RouterUrl} from '../base/RouterUrl';
import {onEvent} from '../base/native/UITrackingAction';
import {Method} from '../util/NativeModulesTools';
import TextUtils from '../util/TextUtils';

const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

interface Props extends NativeStackScreenProps<ParamListBase> {}

interface State extends BaseState {
    init: boolean;
    cardType: 'portrait' | 'national'; // 身份证类型：人像面或国徽面
}

export default class IdCardCapturePreviewPage extends BaseCommPage<Props, State> {
    toSave: string;
    callback?: Function;

    constructor(props: Props) {
        super(props);
        this.state = {
            init: false,
            cardType: 'portrait', // 默认人像面
        };
        this.toSave = this.getParams().toSave ?? '';
        this.callback = this.getCallBack();
        
        // 获取传入的卡片类型
        const cardType = this.getParams().cardType;
        if (cardType === 'national' || cardType === 'portrait') {
            this.state.cardType = cardType;
        }
    }

    componentDidMount() {
        super.componentDidMount();
        this.setState({init: true});
    }

    /**
     * 相册上传
     */
    uploadFromAlbum = () => {
        const {cardType} = this.state;
        onEvent({
            pageId: cardType === 'portrait' ? 'sfzrxm' : 'sfzghm', 
            tableId: cardType === 'portrait' ? 'sfzrxm_xcsc' : 'sfzghm_xcsc'
        });
        
        Method.openCameraAndLibNew(false, 1, (code, json) => {
            try {
                if (code === 200) {
                    const result = JSON.parse(json);
                    if (result && result.length > 0) {
                        this.uploadImage(result[0]);
                    }
                } else if (code === -2 || code === -3) {
                    const result = JSON.parse(json);
                    Method.showToast(result.msg || '选择图片失败');
                }
            } catch (e) {
                Method.showToast('选择图片失败，请重试');
            }
        });
    };

    /**
     * 开始拍照
     */
    startCapture = () => {
        const {cardType} = this.state;
        onEvent({
            pageId: cardType === 'portrait' ? 'sfzrxm' : 'sfzghm', 
            tableId: cardType === 'portrait' ? 'sfzrxm_kspz' : 'sfzghm_kspz'
        });
        
        Method.openCameraAndLibNew(true, 1, (code, json) => {
            try {
                if (code === 200) {
                    const result = JSON.parse(json);
                    if (result && result.length > 0) {
                        this.uploadImage(result[0]);
                    }
                } else if (code === -2 || code === -3) {
                    const result = JSON.parse(json);
                    Method.showToast(result.msg || '拍照失败');
                }
            } catch (e) {
                Method.showToast('拍照失败，请重试');
            }
        });
    };

    /**
     * 上传图片
     */
    uploadImage = async (filePath: string) => {
        this._showWaitDialog();
        
        try {
            const json = await Method.upFileNew(filePath);
            const result = JSON.parse(json);
            
            if (TextUtils.equals('200', result.code)) {
                this._dismissWait();
                this._showToast('拍摄成功');
                
                // 返回结果给调用页面
                if (this.callback) {
                    const {cardType} = this.state;
                    const resultData = {
                        imageUrl: result.url,
                        cardType: cardType,
                        toSave: this.toSave,
                    };
                    this.callback(resultData);
                }
                this._goBack();
            } else {
                this._dismissWait();
                Method.showToast(result.msg || '上传失败');
            }
        } catch (e) {
            this._dismissWait();
            Method.showToast('上传失败，请重试');
        }
    };

    /**
     * 关闭页面
     */
    closePage = () => {
        const {cardType} = this.state;
        onEvent({
            pageId: cardType === 'portrait' ? 'sfzrxm' : 'sfzghm', 
            tableId: cardType === 'portrait' ? 'sfzrxm_gb' : 'sfzghm_gb'
        });
        this._goBack();
    };

    render() {
        const {cardType} = this.state;
        const isPortrait = cardType === 'portrait';
        
        // 根据卡片类型显示不同的内容
        const title = isPortrait ? '身份证人像面' : '身份证国徽面';
        const imageSource = isPortrait 
            ? 'certification_driver_license_template_diagram_4' 
            : 'certification_driver_license_template_diagram_5';

        return (
            <View style={styles.container}>
                {/* 关闭按钮 */}
                <UITouchableOpacity 
                    style={styles.closeButton}
                    onPress={this.closePage}
                >
                    <UIImage 
                        source={'certification_close_white'}
                        style={styles.closeIcon}
                    />
                </UITouchableOpacity>

                {this.state.init && (
                    <View style={styles.content}>
                        {/* 左侧：身份证预览区域 */}
                        <View style={styles.leftSection}>
                            <View style={styles.cardFrame}>
                                <UIImage 
                                    source={imageSource}
                                    style={styles.cardImage}
                                    resizeMode="contain"
                                />
                                {/* 四个角的装饰 */}
                                <View style={[styles.corner, styles.topLeft]} />
                                <View style={[styles.corner, styles.topRight]} />
                                <View style={[styles.corner, styles.bottomLeft]} />
                                <View style={[styles.corner, styles.bottomRight]} />
                            </View>
                        </View>

                        {/* 右侧：说明和操作区域 */}
                        <View style={styles.rightSection}>
                            <Text style={styles.title}>{title}</Text>
                            <Text style={styles.subtitle}>请按以下要求进行拍摄</Text>
                            
                            <View style={styles.tipsList}>
                                <View style={styles.tipItem}>
                                    <View style={styles.tipDot} />
                                    <Text style={styles.tipText}>证件完整置于拍摄框内</Text>
                                </View>
                                <View style={styles.tipItem}>
                                    <View style={styles.tipDot} />
                                    <Text style={styles.tipText}>
                                        四角齐全，<Text style={styles.highlightText}>无遮挡</Text>
                                    </Text>
                                </View>
                                <View style={styles.tipItem}>
                                    <View style={styles.tipDot} />
                                    <Text style={styles.tipText}>
                                        将证件从保护套中拿出，<Text style={styles.highlightText}>避免反光</Text>
                                    </Text>
                                </View>
                            </View>

                            {/* 操作按钮 */}
                            <View style={styles.actionButtons}>
                                <UITouchableOpacity 
                                    style={styles.albumButton}
                                    onPress={this.uploadFromAlbum}
                                >
                                    <Text style={styles.albumButtonText}>相册上传</Text>
                                </UITouchableOpacity>
                                
                                <UIButton 
                                    text="开始拍照"
                                    style={styles.captureButton}
                                    onPress={this.startCapture}
                                />
                            </View>
                        </View>
                    </View>
                )}

                {this._initCommView()}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#2C2C2C',
    },
    closeButton: {
        position: 'absolute',
        top: 20,
        left: 20,
        zIndex: 10,
        width: 32,
        height: 32,
        justifyContent: 'center',
        alignItems: 'center',
    },
    closeIcon: {
        width: 24,
        height: 24,
    },
    content: {
        flex: 1,
        flexDirection: 'row',
        paddingHorizontal: 40,
        paddingVertical: 40,
        alignItems: 'center',
    },
    leftSection: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingRight: 60,
    },
    cardFrame: {
        width: 320,
        height: 200,
        justifyContent: 'center',
        alignItems: 'center',
    },
    cardImage: {
        width: '100%',
        height: '100%',
    },
    corner: {
        position: 'absolute',
        width: 20,
        height: 20,
        borderColor: '#4A90E2',
        borderWidth: 3,
    },
    topLeft: {
        top: -10,
        left: -10,
        borderRightWidth: 0,
        borderBottomWidth: 0,
    },
    topRight: {
        top: -10,
        right: -10,
        borderLeftWidth: 0,
        borderBottomWidth: 0,
    },
    bottomLeft: {
        bottom: -10,
        left: -10,
        borderRightWidth: 0,
        borderTopWidth: 0,
    },
    bottomRight: {
        bottom: -10,
        right: -10,
        borderLeftWidth: 0,
        borderTopWidth: 0,
    },
    rightSection: {
        flex: 1,
        justifyContent: 'center',
        paddingLeft: 20,
    },
    title: {
        fontSize: 28,
        fontWeight: '700',
        color: '#FFFFFF',
        marginBottom: 8,
    },
    subtitle: {
        fontSize: 18,
        color: '#CCCCCC',
        marginBottom: 32,
    },
    tipsList: {
        marginBottom: 40,
    },
    tipItem: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        marginBottom: 16,
    },
    tipDot: {
        width: 6,
        height: 6,
        borderRadius: 3,
        backgroundColor: '#4A90E2',
        marginRight: 12,
        marginTop: 9, // 对齐文字
    },
    tipText: {
        fontSize: 18,
        color: '#FFFFFF',
        lineHeight: 24,
        flex: 1,
    },
    highlightText: {
        color: '#FF6B35',
    },
    actionButtons: {
        flexDirection: 'row',
    },
    albumButton: {
        paddingHorizontal: 32,
        paddingVertical: 16,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#666666',
        backgroundColor: 'transparent',
        minWidth: 140,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 20,
    },
    albumButtonText: {
        fontSize: 18,
        color: '#FFFFFF',
        fontWeight: '500',
    },
    captureButton: {
        paddingHorizontal: 32,
        paddingVertical: 16,
        borderRadius: 8,
        backgroundColor: '#4A90E2',
        minWidth: 140,
        justifyContent: 'center',
        alignItems: 'center',
    },
});
