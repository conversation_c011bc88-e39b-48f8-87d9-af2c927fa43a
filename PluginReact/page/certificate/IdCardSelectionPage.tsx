import React from 'react';
import {StyleSheet, Text, View, Dimensions} from 'react-native';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import UITitleView from '../widget/UITitleView';
import UITouchableOpacity from '../widget/UITouchableOpacity';
import UIImage from '../widget/UIImage';
import {RouterUtils} from '../util/RouterUtils';
import {RouterUrl} from '../base/RouterUrl';
import {onEvent} from '../base/native/UITrackingAction';

const {width: screenWidth} = Dimensions.get('window');

interface State extends BaseState {
    init: boolean;
}

/**
 * 注释: 身份证选择前置页面
 * 时间: 2025/01/31
 * <AUTHOR> Assistant
 */
export default class IdCardSelectionPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    toSave?: string;
    callback?: Function;

    constructor(props) {
        super(props);
        this.state = {
            init: false,
        };
        this.toSave = this.getParams().toSave ?? '';
        this.callback = this.getCallBack();
    }

    componentDidMount() {
        super.componentDidMount();
        this.setState({init: true});
        onEvent({pageId: 'sfzxz', tableId: 'sfzxz', event: 'view'});
    }

    /**
     * 跳转到身份证国徽面拍摄
     */
    onSelectNationalEmblem = () => {
        onEvent({pageId: 'sfzxz', tableId: 'sfzxz_ghm'});
        RouterUtils.skipRouter(RouterUrl.ReactPage, {
            page: 'IdCardCapturePageNational',
            data: {
                toSave: this.toSave,
                cardType: 'national', // 国徽面
            },
            callBack: this.callback,
        });
    };

    /**
     * 跳转到身份证人像面拍摄
     */
    onSelectPortrait = () => {
        onEvent({pageId: 'sfzxz', tableId: 'sfzxz_rxm'});
        RouterUtils.skipRouter(RouterUrl.ReactPage, {
            page: 'IdCardCapturePagePortrait',
            data: {
                toSave: this.toSave,
                cardType: 'portrait', // 人像面
            },
            callBack: this.callback,
        });
    };

    render() {
        return (
            <View style={styles.container}>
                <UITitleView 
                    title={'身份证拍摄'} 
                    clickBack={() => {
                        this._goBack();
                        onEvent({pageId: 'sfzxz', tableId: 'sfzxz_fh'});
                    }}
                />
                
                {this.state.init && (
                    <View style={styles.content}>
                        {/* 标题说明 */}
                        <View style={styles.titleSection}>
                            <Text style={styles.mainTitle}>请按以下要求进行拍摄</Text>
                            <View style={styles.tipsList}>
                                <View style={styles.tipItem}>
                                    <View style={styles.tipDot} />
                                    <Text style={styles.tipText}>证件完整置于拍摄框内</Text>
                                </View>
                                <View style={styles.tipItem}>
                                    <View style={styles.tipDot} />
                                    <Text style={styles.tipText}>四角齐全，无遮挡</Text>
                                </View>
                                <View style={styles.tipItem}>
                                    <View style={styles.tipDot} />
                                    <Text style={styles.tipText}>将证件从保护套中拿出，避免反光</Text>
                                </View>
                            </View>
                        </View>

                        {/* 身份证选择区域 */}
                        <View style={styles.cardSelectionArea}>
                            {/* 身份证国徽面 */}
                            <UITouchableOpacity 
                                style={styles.cardContainer}
                                onPress={this.onSelectNationalEmblem}
                            >
                                <View style={styles.cardImageContainer}>
                                    <UIImage 
                                        source={'certification_driver_license_template_diagram_5'} 
                                        style={styles.cardImage}
                                        resizeMode="contain"
                                    />
                                    <View style={styles.cameraOverlay}>
                                        <UIImage
                                            source={'certification_cys_take_picture'}
                                            style={styles.cameraIcon}
                                        />
                                    </View>
                                </View>
                                <Text style={styles.cardTitle}>身份证国徽面</Text>
                                <Text style={styles.cardSubtitle}>请按以下要求进行拍摄</Text>
                            </UITouchableOpacity>

                            {/* 身份证人像面 */}
                            <UITouchableOpacity 
                                style={styles.cardContainer}
                                onPress={this.onSelectPortrait}
                            >
                                <View style={styles.cardImageContainer}>
                                    <UIImage 
                                        source={'certification_driver_license_template_diagram_4'} 
                                        style={styles.cardImage}
                                        resizeMode="contain"
                                    />
                                    <View style={styles.cameraOverlay}>
                                        <UIImage
                                            source={'certification_cys_take_picture'}
                                            style={styles.cameraIcon}
                                        />
                                    </View>
                                </View>
                                <Text style={styles.cardTitle}>身份证人像面</Text>
                                <Text style={styles.cardSubtitle}>请按以下要求进行拍摄</Text>
                            </UITouchableOpacity>
                        </View>
                    </View>
                )}
                
                {this._initCommView()}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5f5f5',
    },
    content: {
        flex: 1,
        paddingHorizontal: 16,
        paddingTop: 20,
    },
    titleSection: {
        backgroundColor: '#fff',
        borderRadius: 8,
        padding: 16,
        marginBottom: 20,
    },
    mainTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 16,
    },
    tipsList: {
        marginLeft: 8,
    },
    tipItem: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
    },
    tipDot: {
        width: 4,
        height: 4,
        borderRadius: 2,
        backgroundColor: '#5086fc',
        marginRight: 8,
    },
    tipText: {
        fontSize: 14,
        color: '#666',
        flex: 1,
    },
    cardSelectionArea: {
        flex: 1,
    },
    cardContainer: {
        backgroundColor: '#fff',
        borderRadius: 8,
        padding: 16,
        marginBottom: 16,
        alignItems: 'center',
    },
    cardImageContainer: {
        position: 'relative',
        width: screenWidth * 0.7,
        height: 120,
        marginBottom: 12,
    },
    cardImage: {
        width: '100%',
        height: '100%',
        borderRadius: 8,
    },
    cameraOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
    },
    cameraIcon: {
        width: 32,
        height: 32,
        tintColor: '#fff',
    },
    cardTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 4,
    },
    cardSubtitle: {
        fontSize: 13,
        color: '#666',
    },
});
