# 身份证拍摄前置页面使用说明

## 概述

`IdCardCapturePreviewPage` 是一个统一的身份证拍摄前置页面，支持人像面和国徽面两种模式。页面采用横屏布局，根据传入的 `cardType` 参数动态显示不同的内容。

## 功能特点

1. **统一页面**：一个页面支持人像面和国徽面两种模式
2. **横屏布局**：专为横屏设计的UI布局
3. **动态内容**：根据卡片类型显示对应的说明文字和示例图片
4. **完整功能**：支持拍照和相册上传两种方式

## 使用方法

### 调用方式

```typescript
RouterUtils.skipRouter(RouterUrl.ReactPage, {
    page: 'IdCardCapturePreviewPage',
    data: {
        toSave: this.toSave,
        cardType: 'portrait', // 'portrait' 或 'national'
    },
    callBack: (result) => {
        if (result && result.imageUrl) {
            // 处理返回的图片URL
            console.log('图片URL:', result.imageUrl);
            console.log('卡片类型:', result.cardType);
        }
    },
    action: 'replace',
});
```

### 参数说明

- `cardType`: 
  - `'portrait'` - 身份证人像面
  - `'national'` - 身份证国徽面
- `toSave`: 保存标识
- `callBack`: 回调函数，返回拍摄结果

### 返回数据格式

```typescript
{
    imageUrl: string,    // 上传后的图片URL
    cardType: string,    // 卡片类型 'portrait' | 'national'
    toSave: string,      // 保存标识
}
```

## 页面布局

### 横屏布局结构

```
┌─────────────────────────────────────────────────────────┐
│ [X]                                                     │
│                                                         │
│  ┌─────────────────┐    身份证人像面/国徽面              │
│  │                 │    请按以下要求进行拍摄              │
│  │   身份证示例图   │                                    │
│  │                 │    • 证件完整置于拍摄框内            │
│  │                 │    • 四角齐全，无遮挡               │
│  └─────────────────┘    • 将证件从保护套中拿出，避免反光  │
│                                                         │
│                         [相册上传]  [开始拍照]           │
└─────────────────────────────────────────────────────────┘
```

## 集成说明

该页面已集成到现有的身份证认证流程中：

1. 在 `CertificationAuthenticationPage.tsx` 中，当用户点击身份证上传区域时
2. 如果没有图片，会跳转到 `IdCardCapturePreviewPage`
3. 根据点击的是人像面还是国徽面，传入对应的 `cardType`
4. 用户完成拍摄后，图片会自动回填到原页面

## 注意事项

1. 页面设计为横屏模式，请确保在横屏环境下测试
2. 图片上传成功后会自动返回上一页面
3. 支持相册选择和拍照两种方式
4. 包含完整的错误处理和用户提示
