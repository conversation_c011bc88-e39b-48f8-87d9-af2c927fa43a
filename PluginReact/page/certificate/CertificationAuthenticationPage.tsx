import {ScrollView, StyleSheet, Text, TextInput, View} from 'react-native';
import React from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import BaseCommPage, {BaseState, DialogBuilder} from '../base/BaseCommPage';
import {EDriverLicense, EIDCard, EReplenish, EUserLicense, EVehicle, ReqQueryUserLicense, showErrorToast} from './requests/ReqQueryUserLicense';
import {FaceInfo} from './requests/ReqFaceInfo';
import TextUtils from '../util/TextUtils';
import {RouterUtils} from '../util/RouterUtils';
import {RouterUrl} from '../base/RouterUrl';
import {FaceRecognitionParams, Method} from '../util/NativeModulesTools';
import {ReqFaceRegoin} from './requests/ReqFaceRegoin';
import {ReqCarrierBossSubmit2Examing} from './requests/ReqCarrierBossSubmit2Examing';
import {onEvent} from '../base/native/UITrackingAction';
import ComCertificationUploadImgView from './views/ComCertificationUploadImgView';
import UITouchableOpacity from '../widget/UITouchableOpacity';
import UIImage from '../widget/UIImage';
import UIButton from '../widget/UIButton';
import UIServerAndHelpView from './views/UIServerAndHelpView';
import UIDatePicker from '../widget/UIDatePicker';
import {dateFormat} from '../util/DateUtil';
import ThreeElementsTipsDialog from './views/ThreeElementsTipsDialog';
import UITitleView from '../widget/UITitleView';
import {canRegister, isIDCardTakePhote} from './utils/Utils';
import {QuserPromote, ReqUserPromote} from './requests/ReqUserPromote';
import {ReqAuthIdCard} from './requests/ReqAuthIdCard';
import {UserType} from '../user/models/UserType';
import LanguageType from '../util/language/LanguageType';
import {ReqUserUpgradeSaveLicense} from './requests/ReqUserUpgradeSaveLicense';
import {ReqCheckCarrierUserIdCardOccupied} from './requests/ReqCheckCarrierUserIdCardOccupied';
import CertificationForFriverExistDailog from './views/CertificationForFriverExistDailog';
import {ReqFaceRecognitionForVideoPick} from '../pick/requests/ReqFaceRecognitionForVideoPick';
import {ReqCarrierOneClickReplacement} from './requests/ReqCarrierOneClickReplacement';
import CertificationForFriverExistSuccessDailog from './views/CertificationForFriverExistSuccessDailog';
import {ReqLicenseInputAccuracyCheck} from './requests/ReqLicenseInputAccuracyCheck';

interface State extends BaseState {
    init: boolean; //初始化
    cl2: boolean; //确认信息
    cl4: boolean; //姓名
    cl5: boolean; //身份证
    cl3: boolean; //有效期
    timeShow: boolean; //时间选择弹窗
    idCardNo?: string; //身份证号
    idCardName?: string; //姓名
    idCardEffectDate?: string; //身份证有效期
    showThreeElementsTipsDialog: boolean; //三要素弹窗
    showCertificationForFriverExistDailog: boolean; //承运方APP找回账号流程简化弹窗
    showCertificationForFriverExistSuccessDailog: boolean; //承运方APP找回账号流程简化弹窗
    idCardNameText?: string; //身份证姓名错误提示
    idCardNoText?: string; //身份证号错误提示
    idCardEffectDateText?: string; //身份证有效期错误提示
}

/**
 * 注释: 身份认证 (车老板 && 司机
 * 时间: 2024/12/12 10:22
 * <AUTHOR>
 */
export default class CertificationAuthenticationPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageDetail?: EUserLicense;
    frontIdCardUrl?: string; //正面
    negativeIdCardUrl?: string; //背面
    // 三要素弹窗
    dialogTitle = '';
    dialogContent = '';
    dialogCancelable = true;
    dialogLeftCallback: Function;
    dialogRightCallback: Function;
    // 承运方APP找回账号流程简化弹窗
    dialog2Content = '';
    dialog2LeftCallback: Function;
    dialog2RightCallback: Function;
    faceInfo?: FaceInfo;
    typeOcr = '';
    toSave?: string; //司机用
    occupiedMobile?: string; //司机用
    callback?: Function;

    constructor(props) {
        super(props);
        this.state = {
            init: false,
            cl2: false,
            cl4: false,
            cl5: false,
            cl3: false,
            timeShow: false,
            showThreeElementsTipsDialog: false,
            showCertificationForFriverExistDailog: false,
            showCertificationForFriverExistSuccessDailog: false,
        };
        if (UserType.isCarrier()) {
            this.toSave = this.getParams().toSave ?? '';
        }
        this.callback = this.getCallBack();
    }

    componentDidMount() {
        super.componentDidMount();
        this.queryUserLicense();

        // 处理从拍摄页面返回的数据
        const params = this.getParams();
        if (params.fromCapture) {
            this.frontIdCardUrl = params.frontIdCardUrl;
            this.negativeIdCardUrl = params.negativeIdCardUrl;

            // 如果有图片URL，进行OCR识别
            if (this.frontIdCardUrl) {
                this.performOCRForFrontCard();
            }
            if (this.negativeIdCardUrl) {
                this.performOCRForBackCard();
            }
        }

        onEvent({pageId: 'sfzscy', tableId: 'sfzscy', event: 'view'});
    }

    componentWillUnmount() {
        super.componentWillUnmount();
        this.state.timeShow && this.setState({timeShow: false});
    }

    /**
     * 注释：认证详情
     * 时间：2024/12/12 15:28
     * 作者：王家辉
     */
    queryUserLicense() {
        let req = new ReqQueryUserLicense();
        this._showWaitDialog();
        req.request().then((res) => {
            this._dismissWait();
            if (res.isSuccess()) {
                this.pageDetail = res.data;
                if (UserType.isCarrier()) {
                    let mSaveDriverStatus = res.data?.driverLicense?.licenseStatus;
                    let mSaveCarPremissionStatus = res.data?.vehicle?.licenseStatus;
                    /*身份证的时候，判断其他两项有没有完成*/
                    let flag = (TextUtils.equals('1', mSaveDriverStatus) || TextUtils.equals('4', mSaveDriverStatus)) && (TextUtils.equals('1', mSaveCarPremissionStatus) || TextUtils.equals('4', mSaveCarPremissionStatus));
                    this.toSave = flag ? '1' : '0';
                }
                this.setState({init: true});
                if (
                    !TextUtils.isEmpty(this.pageDetail?.idCard?.idCardNo) &&
                    !TextUtils.isEmpty(this.pageDetail?.idCard?.idCardName) &&
                    !TextUtils.isEmpty(this.pageDetail?.idCard?.frontIdCardUrl) &&
                    !TextUtils.isEmpty(this.pageDetail?.idCard?.negativeIdCardUrl) &&
                    !TextUtils.isEmpty(this.pageDetail?.idCard?.idCardEffectDate)
                ) {
                    this.frontIdCardUrl = this.pageDetail?.idCard?.frontIdCardUrl;
                    this.negativeIdCardUrl = this.pageDetail?.idCard?.negativeIdCardUrl;
                    this.setState({
                        idCardNo: this.pageDetail?.idCard?.idCardNo,
                        idCardName: this.pageDetail?.idCard?.idCardName,
                        idCardEffectDate: this.pageDetail?.idCard?.idCardEffectDate,
                        cl2: true,
                        cl4: true,
                        cl5: true, //身份证
                        cl3: true, //有效期
                    });
                }
            } else {
                this.pageDetail = new EUserLicense();
                this.pageDetail.replenish = new EReplenish();
                this.pageDetail.vehicle = new EVehicle();
                this.pageDetail.vehicle.licenseStatus = '3';
                this.pageDetail.driverLicense = new EDriverLicense();
                this.pageDetail.driverLicense.licenseStatus = '3';
                this.pageDetail.idCard = new EIDCard();
                this.pageDetail.idCard.licenseStatus = '3';
                this.pageDetail.idCard.haveIdCardNoFlag = '1';
                this.setState({init: true});
            }
        });
    }

    commit() {
        onEvent({pageId: 'sfzscy', tableId: 'sfzscy_tjscan'});
        if (TextUtils.isEmpty(this.frontIdCardUrl)) {
            this._showToast('请上传身份证正面照片！');
            return;
        }
        if (TextUtils.isEmpty(this.negativeIdCardUrl)) {
            this._showToast('请上传身份证反面照片！');
            return;
        }
        if (TextUtils.isEmpty(this.state.idCardName)) {
            this._showToast('姓名不能为空');
            return;
        }
        if (TextUtils.isEmpty(this.state.idCardNo)) {
            this._showToast('身份证不能为空');
            return;
        }
        if (TextUtils.isEmpty(this.state.idCardEffectDate)) {
            this._showToast('请选择身份证有效期！');
            return;
        }
        this.licenseInputAccuracyCheck();
    }

    checkCarrierUserIdCardOccupied() {
        let req = new ReqCheckCarrierUserIdCardOccupied();
        req.idCard = this.state.idCardNo;
        this._showWaitDialog();
        req.request().then((response) => {
            if (response.isSuccess()) {
                let size = response.data?.carrierIdCardOccupiedUserInfoResp?.occupiedNum ?? 0;
                if (size > 1) {
                    let dialog = new DialogBuilder();
                    dialog.title = '提示';
                    dialog.msg = '该身份证下有多个已认证司机账号，请联系客服处理';
                    dialog.model = 1;
                    dialog.okTxt = '我知道了';
                    this._showDialog(dialog);
                } else if (size == 1) {
                    this.occupiedMobile = response.data?.carrierIdCardOccupiedUserInfoResp?.mobile;
                    this.dialog2Content = `该身份证号已被账号【%s】手机号【%s】占用，请确认是否直接使用当前登录手机号替换原账号？${response.data?.carrierIdCardOccupiedUserInfoResp?.userName}${this.occupiedMobile}`;
                    this.dialog2LeftCallback = () => {
                        //放弃
                        this.setState({showCertificationForFriverExistDailog: false});
                        this.userUpgradeSaveLicense();
                    };
                    this.dialog2RightCallback = () => {
                        this.setState({showCertificationForFriverExistDailog: false});
                        this.liveImage();
                    };
                    this.setState({showCertificationForFriverExistDailog: true});
                } else {
                    this.userUpgradeSaveLicense();
                }
            } else {
                this._dismissWait();
                this._showMsgDialog(response.getMsg());
            }
        });
    }

    /**
     * 提交认证
     */
    userUpgradeSaveLicense() {
        let req = new ReqUserUpgradeSaveLicense();
        req.idCard = new EIDCard();
        req.idCard.haveIdCardNoFlag = '1';
        req.idCard.idCardName = this.state.idCardName;
        req.idCard.idCardNo = this.state.idCardNo;
        req.idCard.idCardEffectDate = this.state.idCardEffectDate;
        req.idCard.frontIdCardUrl = this.frontIdCardUrl;
        req.idCard.negativeIdCardUrl = this.negativeIdCardUrl;
        req.type = '1';
        req.request().then((response) => {
            this._dismissWait();
            if (response.isSuccess()) {
                // 做提交操作
                if (TextUtils.equals('1', this.toSave)) {
                    this.doComitAllMeans();
                } else {
                    this.callback && this.callback();
                    RouterUtils.skipPop();
                }
            } else {
                if (`${response.getMsg()}`.startsWith('该身份证已被占用')) {
                    let dialog = new DialogBuilder();
                    dialog.title = '提示';
                    dialog.msg = response.getMsg();
                    dialog.okTxt = '找回帐号';
                    dialog.onOkEvent = this.liveImage2;
                    this._showDialog(dialog);
                } else {
                    this._showMsgDialog(response.getMsg());
                }
            }
        });
    }
    liveImage2 = () => {
        //找回账号人脸识别专用（不区分角色）
        let params = new FaceRecognitionParams();
        params.idCardName = this.state.idCardName ?? '';
        params.idCardNo = this.state.idCardNo ?? '';
        params.callBack = (response) => {
            //人脸识别返回
            this._showWaitDialog();
            let responseNew = JSON.parse(response);
            let req = new ReqFaceRecognitionForVideoPick();
            let faceInfo = JSON.parse(responseNew.faceInfo);
            req.idCardName = this.state.idCardName;
            req.idCardNo = this.state.idCardNo;
            req.orderNo = faceInfo?.orderNo;
            req.faceReason = '';
            req.url = 'mms-app/getBackAccount/faceRecognitionV2';
            req.request().then((response) => {
                //查询人脸识别结果
                this._dismissWait();
                if (response.isSuccess()) {
                    RouterUtils.skipRouter(RouterUrl.AccountInfoActivity, {idCard: params.idCardNo, realName: params.idCardName});
                } else {
                    this._showMsgDialog(response.getMsg());
                }
            });
        };
        Method.startFaceRecognition(params);
    };

    doComitAllMeans() {
        let showErrorToast2 = showErrorToast(this.pageDetail?.driverLicense?.licenseStatus);
        if (!TextUtils.isEmpty(showErrorToast2)) {
            let dialog = new DialogBuilder();
            dialog.title = '提示';
            dialog.msg = showErrorToast2;
            dialog.model = 1;
            dialog.okTxt = '我知道了';
            this._showDialog(dialog);
            return;
        }
        let showErrorToast3 = showErrorToast(this.pageDetail?.vehicle?.licenseStatus);
        if (!TextUtils.isEmpty(showErrorToast3)) {
            let dialog = new DialogBuilder();
            dialog.title = '提示';
            dialog.msg = showErrorToast2;
            dialog.model = 1;
            dialog.okTxt = '我知道了';
            this._showDialog(dialog);
            return;
        }
        let vehicleFlag = this.pageDetail?.vehicle?.vehicleFlag;
        let quasiVehicleType = this.pageDetail?.driverLicense?.quasiVehicleType;
        if (!TextUtils.equals('A2', quasiVehicleType) && TextUtils.equals(vehicleFlag, '2')) {
            this._showMsgDialog('准驾车型与车辆类型不符，请修改准驾车型，或前往车辆认证模块修改车辆类型。');
            return;
        }
        RouterUtils.skipPop();
        this.callback && this.callback();
    }

    /**
     * 车老板认证
     */
    memberAuthAddIdCard() {
        let req = new ReqAuthIdCard();
        req.haveIdCardNoFlag = '1';
        req.idCardName = this.state.idCardName;
        req.idCardNo = this.state.idCardNo;
        req.idCardEffectDate = this.state.idCardEffectDate;
        req.frontIdCardUrl = this.frontIdCardUrl;
        req.negativeIdCardUrl = this.negativeIdCardUrl;
        this._showWaitDialog();
        req.request().then((response) => {
            this._dismissWait();
            if (response.isSuccess()) {
                let req = new ReqUserPromote();
                req.request().then((res) => {
                    if (res.isSuccess()) {
                        this.onUserPromoteSuccess(res.data);
                    } else {
                        this._showMsgDialog(res.getMsg());
                    }
                });
            } else {
                this._showToast(response.getMsg());
            }
        });
    }

    /**
     * 司机认证审批
     */
    onUserPromoteSuccess(quserPromote?: QuserPromote) {
        if (quserPromote == null) {
            return;
        }
        if (TextUtils.equals('1', quserPromote.threeFactorRiskState)) {
            this.dialogTitle = '当前账号非本人身份证办理的手机号';
            this.dialogContent = '为保障您的账号及后续运输安全，请确保注册手机号是以本人身份证办理';
            this.dialogCancelable = false;
            this.dialogLeftCallback = () => {
                //修改本人手机号
                this.setState({showThreeElementsTipsDialog: false});
                RouterUtils.skipRouter(RouterUrl.UserChangeSelfPhoneActivity, {
                    threeElementsName: quserPromote.threeElementsName,
                    threeElementsIdCardNo: quserPromote.threeElementsIdCardNo,
                    faceReason: quserPromote.faceReason,
                    riskSubmit: quserPromote.riskSubmit,
                    userUpdate: '1',
                });
            };
            this.dialogRightCallback = () => {
                this.setState({showThreeElementsTipsDialog: false});
                if (TextUtils.equals('1', quserPromote.riskSubmit)) {
                    // 申诉过
                    // 待审核/审核驳回
                    // 申诉过
                    // 待审核/审核驳回
                    RouterUtils.skipRouter(RouterUrl.CommVidioDetailRouter, {
                        faceReason: quserPromote.faceReason,
                        idCardNo: quserPromote.threeElementsIdCardNo,
                        FromFrag: '13',
                        isLogin: '1',
                    });
                } else {
                    //跳转常用手机号申诉
                    RouterUtils.skipRouter(RouterUrl.UserComUsePhoneApplyActivity, {
                        threeElementsName: quserPromote.threeElementsName,
                        threeElementsIdCardNo: quserPromote.threeElementsIdCardNo,
                        faceReason: quserPromote.faceReason,
                        riskSubmit: quserPromote.riskSubmit,
                        userUpdate: '1',
                    });
                }
            };
            this.setState({showThreeElementsTipsDialog: true});
        } else if (TextUtils.equals('1', quserPromote.verifyState)) {
            if (TextUtils.equals('1', quserPromote.silentSignState)) {
                let dialog = new DialogBuilder();
                dialog.title = '温馨提示';
                dialog.model = 1;
                dialog.canBack = false;
                dialog.msg = '为保障您的合法权益及在线协议的法律效力，后续运输合同需基于电子签名方式签署。需要您先完成实名认证，同时为免去后续每次签署运输合同需操作意愿认证，本次实名会同步为您完成《静默签授权书》的签署。';
                dialog.okTxt = '同意并继续';
                dialog.onOkEvent = () => {
                    RouterUtils.skipRouter(RouterUrl.SilentProtocolWeActivity, {
                        fromType: '1',
                        faceReason: quserPromote.faceReason,
                        fromFrag: '5',
                        userUpdate: '1',
                        riskSubmit: quserPromote.riskSubmit,
                        idCardName: this.state.idCardName,
                        idCardNo: this.state.idCardNo,
                        carrierOrBoss: '10',
                    });
                };
                this._showDialog(dialog);
            } else {
                let dialog = new DialogBuilder();
                dialog.title = '温馨提示';
                dialog.model = 1;
                dialog.canBack = false;
                dialog.msg = '请完成人脸识别身份验证后操作';
                dialog.okTxt = '去验证';
                dialog.onOkEvent = () => {
                    //再次调用接口判断实名认证状态
                    if (TextUtils.equals('1', quserPromote.riskSubmit)) {
                        RouterUtils.skipRouter(RouterUrl.CommVidioDetailRouter, {
                            faceReason: '1',
                            FromFrag: '5',
                            userUpdate: '1',
                        });
                    } else {
                        //人脸识别
                        this.liveImage();
                    }
                };
                this._showDialog(dialog);
            }
        } else {
            this._showWaitDialog();
            this.carrierBossSubmit2Examing();
        }
    }

    liveImage() {
        let params = new FaceRecognitionParams();
        params.idCardName = this.state.idCardName ?? '';
        params.idCardNo = this.state.idCardNo ?? '';
        params.callBack = (response) => {
            this._showWaitDialog();
            if (UserType.isBoss()) {
                this.faceRecognition(JSON.parse(response));
            } else {
                this.faceRecognitionV2(JSON.parse(response));
            }
        };
        Method.startFaceRecognition(params);
    }

    faceRecognition(response) {
        let req = new ReqFaceRegoin();
        let faceInfo = JSON.parse(response.faceInfo);
        req.idCardName = this.state.idCardName;
        req.idCardNo = this.state.idCardNo;
        req.orderNo = faceInfo?.orderNo;
        req.request().then((response) => {
            if (response.isSuccess()) {
                this.carrierBossSubmit2Examing();
            } else {
                this._dismissWait();
                RouterUtils.skipRouter(RouterUrl.ReactPage, {
                    page: 'FaceRecognitionFailurePage',
                    data: {
                        idcardFrontUrl: this.frontIdCardUrl,
                        idcardReverseUrl: this.negativeIdCardUrl,
                    },
                    callBack: () => {
                        this.liveImage();
                    },
                });
            }
        });
    }

    carrierBossSubmit2Examing() {
        let req = new ReqCarrierBossSubmit2Examing();
        req.request().then((response) => {
            this._dismissWait();
            if (response.isSuccess()) {
                onEvent({
                    pageId: UserType.isBoss() ? 'BossCertificationAuthenticationPage' : 'DriverCertificationAuthenticationPage',
                    tableId: UserType.isBoss() ? 'carownerCertificationSuccessCarrier' : 'carrierCertificationSuccessCarrier',
                });
                RouterUtils.skipRouter(RouterUrl.ReactPage, {
                    page: 'CertificationSubmitSuccessPage',
                    data: {
                        update: false,
                    },
                });
                this.callback && this.callback();
            } else {
                this._showMsgDialog(response.getMsg());
            }
        });
    }

    /**
     * OCR识别
     * @param type
     * @returns {Promise<void>}
     * @param callBack
     */
    async idCardScan(type?: string, callBack?: Function) {
        Method.startOcrInfo(type ?? '', (res) => {
            if (res != null) {
                let filepath = this.refreshTencentCard(JSON.parse(res), this.typeOcr);
                callBack && callBack(filepath);
            } else {
                this._showToast('OCR识别失败');
                return '';
            }
        });
    }

    refreshTencentCard(result: any, type?: string): string {
        if (result == null) {
            return '';
        }
        if (TextUtils.equals('WBOCRSDKTypeFrontSide', type)) {
            if (TextUtils.isEmpty(result.frontFullImageSrc)) {
                this._showMsgDialog('文件损坏，请重新选择文件');
                return '';
            }
            this.setState({idCardNo: result.cardNum, idCardName: result.name, cl2: true, cl4: true, cl5: true});
            Method.putStringExtra('result_front', JSON.stringify(result));
            return result.frontFullImageSrc ?? '';
        } else {
            if (TextUtils.isEmpty(result.backFullImageSrc)) {
                this._showMsgDialog('文件损坏，请重新选择文件');
                return '';
            }
            let validDate = result.validDate;
            if (!TextUtils.isEmpty(validDate)) {
                let validDates: string = validDate.substring(validDate.indexOf('-') + 1);
                let idCardValidDates: string = '长期';

                if (validDates.length === 8) {
                    idCardValidDates = validDates.substring(0, 4) + '-' + validDates.substring(4, 6) + '-' + validDates.substring(6, 8);
                }
                this.setState({idCardEffectDate: idCardValidDates, cl3: true});
            }
            Method.putStringExtra('result_front', JSON.stringify(result));
            return result.backFullImageSrc ?? '';
        }
    }

    /**
     * 对正面身份证进行OCR识别
     */
    performOCRForFrontCard() {
        // 这里可以调用OCR接口进行识别
        // 暂时先设置状态，让用户可以看到确认信息区域
        this.setState({cl2: true, cl4: true, cl5: true});
    }

    /**
     * 对背面身份证进行OCR识别
     */
    performOCRForBackCard() {
        // 这里可以调用OCR接口进行识别
        // 暂时先设置状态，让用户可以看到有效期输入
        this.setState({cl3: true});
    }

    faceRecognitionV2(response) {
        let req = new ReqFaceRecognitionForVideoPick();
        let faceInfo = JSON.parse(response.faceInfo);
        req.idCardName = this.state.idCardName;
        req.idCardNo = this.state.idCardNo;
        req.orderNo = faceInfo?.orderNo;
        req.faceReason = '';
        req.url = 'mms-app/getBackAccount/faceRecognitionV2';
        req.request().then((response) => {
            if (response.isSuccess()) {
                let req2 = new ReqCarrierOneClickReplacement();
                req2.occupiedMobile = this.occupiedMobile;
                req2.request().then((response) => {
                    this._dismissWait();
                    if (response.isSuccess()) {
                        this.setState({showCertificationForFriverExistSuccessDailog: true});
                    } else {
                        RouterUtils.skipRouter(RouterUrl.ReactPage, {
                            page: 'FaceRecognitionFailurePage',
                            data: {
                                idcardFrontUrl: this.frontIdCardUrl,
                                idcardReverseUrl: this.negativeIdCardUrl,
                            },
                            callBack: () => {
                                this.liveImage();
                            },
                        });
                    }
                });
            } else {
                this._dismissWait();
                this._showMsgDialog(response.getMsg());
            }
        });
    }

    /**
     * 证件准确性校验接口
     */
    licenseInputAccuracyCheck() {
        let req = new ReqLicenseInputAccuracyCheck();
        req.type = '1';
        req.frontIdCardUrl = this.frontIdCardUrl;
        req.negativeIdCardUrl = this.negativeIdCardUrl;
        req.idCardName = this.state.idCardName;
        req.idCardNo = this.state.idCardNo;
        req.idCardEffectDate = this.state.idCardEffectDate;
        req.request().then((response) => {
            if (response.isSuccess()) {
                if (UserType.isBoss()) {
                    let register = canRegister(this.state.idCardNo ?? '', 70);
                    if (!register) {
                        this._showMsgDialog('您好，您已超出车老板认证年龄，会员认证失败！如需帮助，请拨打中储智运客服热线400-088-5566');
                        return;
                    }
                    // 车老板认证
                    this.memberAuthAddIdCard();
                } else {
                    // 司机认证
                    this.checkCarrierUserIdCardOccupied();
                }
            } else {
                let idCardEffectDateText = response.data?.data?.idCardEffectDateText;
                let idCardNameText = response.data?.data?.idCardNameText;
                let idCardNoText = response.data?.data?.idCardNoText;
                this.setState({
                    idCardEffectDateText: idCardEffectDateText,
                    idCardNameText: idCardNameText,
                    idCardNoText: idCardNoText,
                });
                if (TextUtils.isNoEmpty(idCardEffectDateText) || TextUtils.isNoEmpty(idCardNameText) || TextUtils.isNoEmpty(idCardNoText)) {
                    return;
                } else {
                    this._showMsgDialog(response.getMsg());
                }
            }
        });
    }

    getInput(title?: string, value?: string) {
        switch (title) {
            case '姓名':
                this.setState({idCardName: value, idCardNameText: ''});
                return;
            case '身份证':
                this.setState({idCardNo: value, idCardNoText: ''});
                return;
        }
    }

    render() {
        return (
            <View style={{flex: 1}}>
                <UITitleView
                    title={'身份认证'}
                    clickBack={() => {
                        this._goBack();
                        onEvent({pageId: 'sfzscy', tableId: 'sfzscy_fh'});
                    }}
                />
                {this.state.init && (
                    <ScrollView>
                        <View
                            style={[
                                styles.row,
                                {
                                    paddingTop: 14,
                                    paddingBottom: 8,
                                    paddingHorizontal: 14,
                                    backgroundColor: '#fff',
                                },
                            ]}>
                            <Text style={{fontSize: 18, color: '#333', fontWeight: 'bold'}}>
                                {LanguageType.getTxt('身份证信息')}
                                <Text style={{color: 'red'}}>*</Text>
                            </Text>
                        </View>
                        <View style={{backgroundColor: '#fff', alignItems: 'center'}}>
                            <ComCertificationUploadImgView
                                ivWarn={false}
                                tvDesc={'拍摄身份证正反面，避免反光，确认文字内容清晰可见'}
                                tvTitle3={''}
                                layout={1}
                                leftImg={{
                                    url: this.frontIdCardUrl ?? '',
                                    defaultValue: 'certification_driver_license_template_diagram_4',
                                    title: '身份证人像面',
                                    onPostSuccess: (url) => {
                                        this.frontIdCardUrl = url;
                                    },
                                    onSelectFile: async (callback: Function) => {
                                        // 如果没有图片，跳转到前置页面
                                        if (!this.frontIdCardUrl) {
                                            RouterUtils.skipRouter(RouterUrl.ReactPage, {
                                                page: 'IdCardCapturePreviewPage',
                                                data: {
                                                    toSave: this.toSave,
                                                    cardType: 'portrait', // 人像面
                                                },
                                                callBack: (result) => {
                                                    if (result && result.imageUrl) {
                                                        this.frontIdCardUrl = result.imageUrl;
                                                        callback(result.imageUrl);
                                                    }
                                                },
                                                action: 'replace',
                                            });
                                            onEvent({pageId: 'sfzscy', tableId: 'sfzscy_dj_rxm'});
                                            return;
                                        }
                                        this.typeOcr = 'WBOCRSDKTypeFrontSide';
                                        await this.idCardScan('WBOCRSDKTypeFrontSide', callback);
                                    },
                                    onDelPic: () => {
                                        this.frontIdCardUrl = '';
                                    },
                                }}
                                rightImg={{
                                    url: this.negativeIdCardUrl ?? '',
                                    defaultValue: 'certification_driver_license_template_diagram_5',
                                    title: '身份证国徽面',
                                    onPostSuccess: (url) => {
                                        this.negativeIdCardUrl = url;
                                    },
                                    onSelectFile: async (callback: Function) => {
                                        // 如果没有图片，跳转到前置页面
                                        if (!this.negativeIdCardUrl) {
                                            RouterUtils.skipRouter(RouterUrl.ReactPage, {
                                                page: 'IdCardCapturePreviewPage',
                                                data: {
                                                    toSave: this.toSave,
                                                    cardType: 'national', // 国徽面
                                                },
                                                callBack: (result) => {
                                                    if (result && result.imageUrl) {
                                                        this.negativeIdCardUrl = result.imageUrl;
                                                        callback(result.imageUrl);
                                                    }
                                                },
                                                action: 'replace',
                                            });
                                            onEvent({pageId: 'sfzscy', tableId: 'sfzscy_dj_ghm'});
                                            return;
                                        }
                                        this.typeOcr = 'WBOCRSDKTypeBackSide';
                                        await this.idCardScan('WBOCRSDKTypeBackSide', callback);
                                    },
                                    onDelPic: () => {
                                        this.negativeIdCardUrl = '';
                                    },
                                }}
                            />
                        </View>
                        {this.state.cl2 && (
                            <View
                                style={{
                                    paddingTop: 14,
                                    paddingBottom: 8,
                                    paddingHorizontal: 14,
                                    backgroundColor: '#fff',
                                    marginTop: 8,
                                }}>
                                <Text style={{fontSize: 18, color: '#333', fontWeight: 'bold'}}>
                                    {LanguageType.getTxt('确认信息')}
                                    <Text style={{color: 'red'}}>*</Text>
                                </Text>
                                <Text
                                    style={{
                                        fontSize: 14,
                                        color: '#666',
                                        marginTop: 8,
                                    }}>
                                    请确认以下信息，如有误请点击上图重拍摄或点击修改信息内容
                                </Text>
                            </View>
                        )}
                        <View style={{backgroundColor: '#fff'}}>
                            {this.state.cl4 && this.renderInputItemView(LanguageType.getTxt('姓名'), this.state.idCardName, true, this.state.idCardNameText)}
                            <View style={{height: 0.5, backgroundColor: '#e3e3e3'}} />
                            {this.state.cl5 && this.renderInputItemView(LanguageType.getTxt('身份证'), this.state.idCardNo, true, this.state.idCardNoText)}
                            <View style={{height: 0.5, backgroundColor: '#e3e3e3'}} />
                            {this.state.cl3 && this.renderInputItemView(LanguageType.getTxt('有效期'), this.state.idCardEffectDate, false, this.state.idCardEffectDateText)}
                        </View>
                        <UIButton text={LanguageType.getTxt('提交上传')} height={55} style={{marginTop: 35, marginHorizontal: 18}} onPress={() => this.commit()} />
                        <UITouchableOpacity
                            style={{marginTop: 20, alignItems: 'center', marginBottom: 25}}
                            onPress={() => {
                                //跳转角色变更
                                RouterUtils.skipRouter(RouterUrl.CertificateRoleChangeActivity, {});
                            }}>
                            <Text style={{fontSize: 14, color: '#5086fc'}}>{'会员类型变更 >'}</Text>
                        </UITouchableOpacity>
                    </ScrollView>
                )}
                <View style={{flex: 1}} />
                <UIServerAndHelpView />
                {/*日期弹窗*/}
                {this.state.timeShow && (
                    <UIDatePicker
                        mode={'date'}
                        title={'请选择有效期'}
                        onHideEvent={() => {
                            this.setState({timeShow: false});
                        }}
                        onSelectEvent={(date) => {
                            this.setState({idCardEffectDate: dateFormat(date, 'yyyy-MM-dd'), idCardEffectDateText: ''});
                        }}
                    />
                )}
                {this.state.showThreeElementsTipsDialog && (
                    <ThreeElementsTipsDialog
                        title={this.dialogTitle}
                        content={this.dialogContent}
                        cancelable={this.dialogCancelable}
                        onRightCallback={this.dialogRightCallback}
                        onLeftCallback={this.dialogLeftCallback}
                        onClose={() => this.setState({showThreeElementsTipsDialog: false})}
                    />
                )}
                {this.state.showCertificationForFriverExistDailog && (
                    <CertificationForFriverExistDailog
                        content={this.dialog2Content}
                        cancelable={false}
                        onRightCallback={this.dialog2RightCallback}
                        onLeftCallback={this.dialog2LeftCallback}
                        onClose={() => this.setState({showCertificationForFriverExistDailog: false})}
                    />
                )}
                {this.state.showCertificationForFriverExistSuccessDailog && (
                    <CertificationForFriverExistSuccessDailog
                        content={`账号替换处理成功，请使用当新手机号【${Method.getLogin().mobile}】重新登录`}
                        cancelable={false}
                        onRightCallback={() => {
                            this.setState({showCertificationForFriverExistSuccessDailog: false});
                            //退出应用
                            Method.onLoseToken();
                        }}
                        onClose={() => this.setState({showCertificationForFriverExistSuccessDailog: false})}
                    />
                )}
                {this._initCommView()}
            </View>
        );
    }

    renderInputItemView(title: string, content?: string, isInput?: boolean, errorMsg?: string) {
        return (
            <View style={{paddingVertical: 7}}>
                <View style={[styles.row, {paddingHorizontal: 14}]}>
                    <View style={{flexDirection: 'row', alignItems: 'center'}}>
                        {TextUtils.isNoEmpty(errorMsg) && <UIImage source={'icon_warning_new'} style={{width: 15, height: 15, marginRight: 5}} />}
                        <Text style={{fontSize: 16, color: '#333'}}>{title}</Text>
                    </View>
                    <UITouchableOpacity
                        activeOpacity={1}
                        style={[
                            styles.row,
                            {
                                justifyContent: 'center',
                                backgroundColor: TextUtils.isNoEmpty(errorMsg) ? '#FFF5F1' : undefined,
                                borderColor: TextUtils.isNoEmpty(errorMsg) ? '#FF5E1C' : undefined,
                                borderWidth: TextUtils.isNoEmpty(errorMsg) ? 0.5 : 0,
                                borderRadius: TextUtils.isNoEmpty(errorMsg) ? 5 : 0,
                                marginRight: 9,
                                paddingVertical: 7,
                                paddingRight: 5,
                            },
                        ]}
                        onPress={() => !isInput && this.setState({timeShow: true})}>
                        {isInput ? (
                            <TextInput
                                style={{
                                    fontSize: 16,
                                    paddingRight: 10,
                                    textAlign: 'right',
                                    maxWidth: 220,
                                    minWidth: 80,
                                    paddingLeft: 14,
                                }}
                                value={content}
                                placeholder={'请输入'}
                                onChangeText={(text) => {
                                    this.getInput(title, text);
                                }}
                            />
                        ) : (
                            <Text
                                style={{
                                    fontSize: 16,
                                    color: content ? '#333' : '#999',
                                    maxWidth: 220,
                                    minWidth: 80,
                                    paddingRight: 10,
                                    paddingLeft: 14,
                                    textAlign: 'right',
                                }}>
                                {content ? content : '请选择'}
                            </Text>
                        )}
                        <UIImage source={TextUtils.isNoEmpty(errorMsg) ? 'certification_fill_in_red' : 'certification_fill_in_gray'} style={{width: 15, height: 15}} />
                    </UITouchableOpacity>
                </View>
                {TextUtils.isNoEmpty(errorMsg) && (
                    <View
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'center',
                            paddingLeft: 15,
                            paddingRight: 15,
                        }}>
                        <Text style={{fontSize: 13, color: '#FF4747', flex: 1}}>{errorMsg}</Text>
                    </View>
                )}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    row: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
});
