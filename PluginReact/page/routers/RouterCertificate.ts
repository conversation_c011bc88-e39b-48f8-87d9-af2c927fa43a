export default [
    {
        title: '证件管理页',
        name: 'CertificateManagerPage',
        component: require('../certificate/CertificateManagerPage').default,
    },
    {
        title: '车辆证件管理页',
        name: 'VehicleIdCardManagerPage',
        component: require('../certificate/VehicleIdCardManagerPage').default,
    },
    {
        title: '证件材料预览页',
        name: 'CertificateMaterialsPreViewPage',
        component: require('../certificate/CertificateMaterialsPreViewPage').default,
    },
    {
        title: '证明材料预览页',
        name: 'ProofMaterialsPreViewPage',
        component: require('../certificate/ProofMaterialsPreViewPage').default,
    },
    {
        title: '人车合影上传-更新-过期',
        name: 'PersonPhotoGroupPage',
        component: require('../certificate/PersonPhotoGroupPage').default,
    },
    {
        title: '临时车牌上传-更新-过期',
        name: 'ProCardPage',
        component: require('../certificate/ProCardPage').default,
    },
    {
        title: '从业资格证上传-更新-过期',
        name: 'QualificationCertificatePage',
        component: require('../certificate/QualificationCertificatePage').default,
    },
    {
        title: '证明材料',
        name: 'ProofMaterialsPage',
        component: require('../certificate/ProofMaterialsPage').default,
    },
    {
        title: '亲属关系证明',
        name: 'KindredMaterialsPage',
        component: require('../certificate/KindredMaterialsPage').default,
    },
    {
        title: '图片预览页',
        name: 'ImagePreViewPage',
        component: require('../base/ImagePreViewPage').default,
    },
    {
        title: '个体司机认证',
        name: 'CyrAuthAllPage',
        component: require('../certificate/CyrAuthAllPage').default,
    },
    {
        title: '个体司机车辆认证',
        name: 'CyrAuthCarPage',
        component: require('../certificate/CyrAuthCarPage').default,
    },
    {
        title: '驾驶证认证页',
        name: 'DriverLicenseAuthPage',
        component: require('../certificate/DriverLicenseAuthPage').default,
    },
    {
        title: '查询电子驾驶证页',
        name: 'QueryElectronicDriverPage',
        component: require('../certificate/QueryElectronicDriverPage').default,
    },
    {
        title: '身份认证',
        name: 'CertificationAuthenticationPage',
        component: require('../certificate/CertificationAuthenticationPage').default,
    },
    {
        title: '身份认证-驳回',
        name: 'CertificationAuthenticationNoPassPage',
        component: require('../certificate/CertificationAuthenticationNoPassPage').default,
    },
    {
        title: '路运营运输经营许可证上传',
        name: 'ExpiredTransportBusinessUpdatePage',
        component: require('../certificate/ExpiredTransportBusinessUpdatePage').default,
    },
    {
        title: '路运营运输许可证上传',
        name: 'ExpiredTransportUpdatePage',
        component: require('../certificate/ExpiredTransportUpdatePage').default,
    },
    {
        title: '认证信息提交成功',
        name: 'CertificationSubmitSuccessPage',
        component: require('../certificate/CertificationSubmitSuccessPage').default,
    },
    {
        title: '身份认证',
        name: 'CertificationAuthenticationPageV1',
        component: require('../certificate/CertificationAuthenticationPageV1').default,
    },
    {
        title: '个体司机添加车辆',
        name: 'CyrCarAddPage',
        component: require('../certificate/CyrCarAddPage').default,
    },
    {
        title: '普通货车行驶证',
        name: 'DrivingLicensePage',
        component: require('../certificate/DrivingLicensePage').default,
    },
    {
        title: '牵引车/车头行驶证',
        name: 'DrivingHeadLicensePage',
        component: require('../certificate/DrivingHeadLicensePage').default,
    },
    {
        title: '挂车/车身行驶证',
        name: 'DrivingGuaLicensePage',
        component: require('../certificate/DrivingGuaLicensePage').default,
    },
    {
        title: '物流企业添加车辆',
        name: 'CysCarAddPage',
        component: require('../certificate/CysCarAddPage').default,
    },
    {
        title: '车老板添加车辆',
        name: 'BossCarAddPage',
        component: require('../certificate/BossCarAddPage').default,
    },
    {
        title: '认证人脸识别失败',
        name: 'FaceRecognitionFailurePage',
        component: require('../certificate/page/FaceRecognitionFailurePage').default,
    },
    {
        title: '身份证拍摄前置页面',
        name: 'IdCardCapturePreviewPage',
        component: require('../certificate/IdCardCapturePreviewPage').default,
    },
];
